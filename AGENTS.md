# Repository Guidelines

## Project Structure & Module Organization
The repository centers on a FashionMNIST DDPM implementation. Key directories:
- `diffusionModels/simpleDiffusion/` holds the diffusion process (`simpleDiffusion.py`) and beta schedules.
- `noisePredictModels/Unet/UNet.py` defines the denoiser backbone; extend this folder for alternative architectures.
- `utils/` provides training orchestration (`trainNetworkHelper.py`) and IO helpers.
- `dataset/fashion/` stores raw FashionMNIST binaries; treat it as read-only in commits.
- `saved_train_models/` keeps finalized checkpoints, while `save_train_models/` is the scratch space for in-progress training.
The `image_test.py` script is the runnable entry point for training and sampling.

## Environment, Build & Run
Use Python 3.10+. Install dependencies in an isolated environment:
```bash
python -m venv .venv && source .venv/bin/activate
pip install torch torchvision numpy
```
Launch a training run and sampler with:
```bash
python image_test.py
```
Override hyperparameters in the script before running; checkpoints will populate the `save*` directories.

## Coding Style & Naming Conventions
Follow PEP 8 with 4-space indentation. Modules and files use lowercase with underscores, classes stay PascalCase, and functions or variables remain snake_case. Keep tensors on the provided `device` and add brief shape comments when introducing new training steps. When adding utilities, expose them via a to-be-created `utils/__init__.py` for cleaner imports.

## Testing Guidelines
There is no dedicated test harness yet; smoke-test changes with a short training loop (`epoches=1`, reduced `timesteps`) in `image_test.py`. If you add new modules, supply `pytest`-ready unit tests under `tests/` and document fixtures. Always ensure sampling completes without runtime errors and that generated images look reasonable.

## Commit & Pull Request Guidelines
This snapshot lacks Git history, so adopt Conventional Commits (`feat:`, `fix:`, `refactor:`) for clarity. Scope each commit to one logical change and mention impacted modules in the body. Pull requests should summarize training impact, list reproducible commands, and attach sample outputs or metrics. Link tracked issues and note any dataset or checkpoint additions to aid reviewers.

## Data & Security Notes
Do not version large FashionMNIST binaries; rely on `torchvision.datasets` to materialize them locally. Treat checkpoints under `saved_train_models/` as optional artifacts—store only those needed for demos. Never commit API keys or external credentials.
